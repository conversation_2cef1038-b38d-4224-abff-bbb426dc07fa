<?xml version="1.0" encoding="UTF-8"?>
<quiz>


<question type="category">
<category>
<text>$course$/test_tikz_final_moodle/Geometría/Volumen de Prismas</text>
</category>
</question>


<question type="multichoice">
<name>
<text> Q1 : volumen_cajas_empaque_interpretacion_representacion_n2_v1 </text>
</name>
<questiontext format="html">
<text><![CDATA[
<style type="text/css" rel="stylesheet">
/* tables with alternating shading */
.table_shade {
    border-collapse: collapse;
    border-spacing: 0;
    border:1px solid #FFFFFF;
    background-color: #FFFFFF;
}
.table_shade th {
    border:1px solid #FFFFFF;
    background: #D5D5D5;
}
.table_shade td {
    border:1px solid #FFFFFF;
}
.table_shade .odd {
    background: #EEEEEE;
}
.table_shade .even {
    background: #FBFBFB;
}
</style>
<p>
<p>Una empresa de logística alimentaria necesita evaluar dos tipos de cajas para el empaque de sus productos alimenticios. En la siguiente imagen se muestran las dimensiones de ambas cajas:</p>
<p><img src="@@PLUGINFILE@@/cajas_volumen_3d.png" style="width:16cm" /></p>
<ul>
<li><strong>Caja 1</strong>: Paralelepípedo rectangular de 30 cm × 12 cm × 8 cm</li>
<li><strong>Caja 2</strong>: Cubo de 12 cm de lado</li>
</ul>
<p>¿Cuál es el volumen de la caja que ocupa mayor espacio?</p>
</p>]]></text>
<file name="cajas_volumen_3d.png" encoding="base64">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</file>
</questiontext>
<generalfeedback format="html">
<text><![CDATA[<p>
<p>Para resolver este problema, debemos calcular el volumen de cada caja y compararlos.</p>
<p><strong>Cálculo del volumen de la Caja 1 (paralelepípedo):</strong>
- Volumen = largo × ancho × alto
- Volumen_1 = 30 cm × 12 cm × 8 cm = 2880 cm³</p>
<p><strong>Cálculo del volumen de la Caja 2 (cubo):</strong>
- Volumen = lado³
- Volumen_2 = 12³ cm³ = 1728 cm³</p>
<p><strong>Comparación:</strong>
- Caja 1: 2880 cm³
- Caja 2: 1728 cm³</p>
<p>Por lo tanto, la caja 2 ocupa más volumen que la caja 1. La respuesta correcta es <strong>2880 cm³</strong>.</p>
</p>]]></text>
</generalfeedback>
<penalty>0</penalty>
<defaultgrade>1</defaultgrade>
<shuffleanswers>false</shuffleanswers>
<single>true</single>
<answernumbering>abc</answernumbering>
<answer fraction="0" format="html">
<text><![CDATA[<p>
2160 cm³ porque representa el 75% del volumen mayor
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Falso: porque representa el 75% del volumen mayor
</p>]]></text>
</feedback>
</answer>
<answer fraction="0" format="html">
<text><![CDATA[<p>
2304 cm³ porque es el promedio de ambos volúmenes
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Falso: porque es el promedio de ambos volúmenes
</p>]]></text>
</feedback>
</answer>
<answer fraction="0" format="html">
<text><![CDATA[<p>
1440 cm³ porque es la mitad del volumen de la caja 1
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Falso: porque es la mitad del volumen de la caja 1
</p>]]></text>
</feedback>
</answer>
<answer fraction="100" format="html">
<text><![CDATA[<p>
1728 cm³ porque el volumen de la caja 2 es 1728 cm³
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Verdadero: porque el volumen de la caja 2 es 1728 cm³
</p>]]></text>
</feedback>
</answer>
</question>

</quiz>
